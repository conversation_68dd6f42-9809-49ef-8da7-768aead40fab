import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'

export interface SessionData {
  isAuthenticated: boolean
  userInfo?: {
    id: string
    displayName: string
    userPrincipalName: string
    mail?: string
  }
  lastLoginTime?: number
  expiresAt?: number
}

export class SessionService {
  private sessionPath: string
  private session: SessionData

  constructor() {
    console.log('🔧 Initializing SessionService...')
    this.sessionPath = this.getSessionPath()
    this.session = this.loadSession()
    console.log('✅ SessionService initialized')
  }

  private getSessionPath(): string {
    // 获取用户数据目录
    // Windows: C:\Users\<USER>\AppData\Roaming\od-commander
    // macOS: ~/Library/Application Support/od-commander
    // Linux: ~/.config/od-commander
    const userDataPath = app.getPath('userData')
    const sessionDir = path.join(userDataPath, 'session')

    console.log('📁 User data path:', userDataPath)
    console.log('📁 Session directory:', sessionDir)

    // 确保会话目录存在
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true })
      console.log('📁 Created session directory:', sessionDir)
    }

    const sessionPath = path.join(sessionDir, 'session.json')
    console.log('📁 Session file path:', sessionPath)
    return sessionPath
  }

  private loadSession(): SessionData {
    try {
      console.log('🔍 Checking for existing session at:', this.sessionPath)

      if (fs.existsSync(this.sessionPath)) {
        console.log('✅ Session file found, loading...')
        const sessionData = fs.readFileSync(this.sessionPath, 'utf-8')
        const loadedSession = JSON.parse(sessionData)

        console.log('📄 Session data loaded:', {
          isAuthenticated: loadedSession.isAuthenticated,
          userDisplayName: loadedSession.userInfo?.displayName,
          lastLoginTime: loadedSession.lastLoginTime ? new Date(loadedSession.lastLoginTime).toLocaleString() : 'N/A',
          expiresAt: loadedSession.expiresAt ? new Date(loadedSession.expiresAt).toLocaleString() : 'N/A'
        })

        // 检查会话是否过期
        if (loadedSession.expiresAt && Date.now() > loadedSession.expiresAt) {
          console.log('⏰ Session expired, clearing...')
          this.clearSession()
          return { isAuthenticated: false }
        }

        console.log('✅ Session loaded successfully and is valid')
        return loadedSession
      } else {
        console.log('❌ No session file found')
      }
    } catch (error) {
      console.error('❌ Failed to load session:', error)
    }

    return { isAuthenticated: false }
  }

  public saveSession(sessionData: SessionData): void {
    try {
      const sessionDir = path.dirname(this.sessionPath)
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true })
        console.log('📁 Created session directory:', sessionDir)
      }

      // 设置会话过期时间（7天）
      const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000)
      const sessionToSave = {
        ...sessionData,
        lastLoginTime: Date.now(),
        expiresAt
      }

      console.log('💾 Saving session to:', this.sessionPath)
      console.log('📄 Session data to save:', {
        isAuthenticated: sessionToSave.isAuthenticated,
        userDisplayName: sessionToSave.userInfo?.displayName,
        lastLoginTime: new Date(sessionToSave.lastLoginTime).toLocaleString(),
        expiresAt: new Date(sessionToSave.expiresAt).toLocaleString()
      })

      fs.writeFileSync(this.sessionPath, JSON.stringify(sessionToSave, null, 2), 'utf-8')
      this.session = sessionToSave
      console.log('✅ Session saved successfully')
    } catch (error) {
      console.error('❌ Failed to save session:', error)
      throw new Error('保存会话失败')
    }
  }

  public getSession(): SessionData {
    return { ...this.session }
  }

  public isSessionValid(): boolean {
    if (!this.session.isAuthenticated) {
      return false
    }
    
    // 检查会话是否过期
    if (this.session.expiresAt && Date.now() > this.session.expiresAt) {
      console.log('Session expired')
      this.clearSession()
      return false
    }
    
    return true
  }

  public clearSession(): void {
    try {
      if (fs.existsSync(this.sessionPath)) {
        fs.unlinkSync(this.sessionPath)
      }
      this.session = { isAuthenticated: false }
      console.log('Session cleared')
    } catch (error) {
      console.error('Failed to clear session:', error)
    }
  }

  public updateUserInfo(userInfo: SessionData['userInfo']): void {
    if (this.session.isAuthenticated) {
      this.session.userInfo = userInfo
      this.saveSession(this.session)
    }
  }
}
