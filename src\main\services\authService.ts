import { PublicClientApplication, Configuration, AuthenticationResult, InteractionRequiredAuthError } from '@azure/msal-node'
import { BrowserWindow, shell, dialog } from 'electron'
import { join } from 'path'
import * as crypto from 'crypto'
import * as url from 'url'

export interface AuthConfig {
  clientId: string
  clientSecret: string
  authority: string
  redirectUri: string
}

export class AuthService {
  private msalApp: PublicClientApplication
  private config: AuthConfig
  private currentAccessToken: string | null = null
  private tokenExpiresAt: number | null = null
  private currentRefreshToken: string | null = null
  private autoRefreshTimer: NodeJS.Timeout | null = null

  constructor(config: AuthConfig) {
    this.config = config

    // 验证配置并提供更好的错误信息
    if (!config.clientId) {
      throw new Error('❌ 缺少 Azure 应用客户端 ID，请检查配置文件')
    }

    if (!config.clientSecret) {
      console.warn('⚠️ 缺少 Azure 应用客户端密钥')
      console.warn('📝 这将限制某些认证功能，建议配置完整的客户端密钥')
      console.log('📖 详细设置说明请查看 README_AZURE_SETUP.md')
    } else {
      console.log('✅ Azure 应用配置完整，支持完整认证功能')
    }
    
    const msalConfig: Configuration = {
      auth: {
        clientId: config.clientId,
        authority: config.authority,
      },
      system: {
        loggerOptions: {
          loggerCallback: (level, message, _containsPii) => {
            if (level <= 2) { // Only log errors and warnings
              console.log(`[MSAL] ${message}`)
            }
          },
          piiLoggingEnabled: false,
          logLevel: 2, // Warning level
        },
      },
    }

    this.msalApp = new PublicClientApplication(msalConfig)
    
    console.log('🔧 Azure应用配置已加载:', {
      clientId: config.clientId.substring(0, 8) + '...',
      authority: config.authority,
      redirectUri: config.redirectUri
    })
  }

  /**
   * 主要的授权方法 - 使用Authorization Code Flow with PKCE (长期refresh token管理)
   */
  async authenticate(forceNewLogin: boolean = false): Promise<AuthenticationResult> {
    const scopes = [
      'https://graph.microsoft.com/Files.ReadWrite.All',
      'https://graph.microsoft.com/User.Read',
      'offline_access' // 关键：获取90天refresh token
    ]

    try {
      console.log('Starting authentication with Authorization Code Flow + PKCE...')
      console.log('Force new login:', forceNewLogin)

      // 如果不是强制新登录，首先尝试静默获取token（如果已有有效的refresh token）
      if (!forceNewLogin) {
        const accounts = await this.msalApp.getTokenCache().getAllAccounts()
        if (accounts.length > 0) {
          try {
            console.log('Attempting silent token acquisition...')
            const silentRequest = {
              account: accounts[0],
              scopes: scopes,
              forceRefresh: false
            }

            const response = await this.msalApp.acquireTokenSilent(silentRequest)
            console.log('Silent token acquisition successful - using cached refresh token')
            this.updateTokenCache(response)
            this.setupAutoRefresh()
            return response
          } catch (silentError) {
            console.log('Silent token acquisition failed:', silentError instanceof Error ? silentError.message : String(silentError))

            // 如果是InteractionRequiredAuthError，说明需要用户交互
            if (silentError instanceof InteractionRequiredAuthError) {
              console.log('User interaction required, proceeding with Authorization Code Flow')
            }
          }
        }
      } else {
        console.log('Force new login requested, skipping silent token acquisition')
      }

      // 使用1Panel风格的Authorization Code Flow - 按用户要求
      const result = await this.authenticateWith1PanelFlow(scopes)
      this.updateTokenCache(result)
      this.setupAutoRefresh()
      return result
    } catch (error) {
      console.error('Authentication failed:', error)
      throw error
    }
  }

  /**
   * 1Panel风格的Authorization Code Flow - 用户手动输入授权码
   */
  private async authenticateWith1PanelFlow(scopes: string[]): Promise<AuthenticationResult> {
    const { shell, dialog } = require('electron')
    const crypto = require('crypto')
    
    // 生成PKCE参数
    const code_verifier = crypto.randomBytes(32).toString('base64url')
    const code_challenge = crypto.createHash('sha256').update(code_verifier).digest('base64url')
    
    // 构建授权URL
    const authUrl = `${this.config.authority}/oauth2/v2.0/authorize?` +
      `client_id=${this.config.clientId}&` +
      `response_type=code&` +
      `redirect_uri=${encodeURIComponent(this.config.redirectUri)}&` +
      `scope=${encodeURIComponent(scopes.join(' '))}&` +
      `code_challenge=${code_challenge}&` +
      `code_challenge_method=S256&` +
      `response_mode=query&` +
      `prompt=select_account`
    
    return new Promise((resolve, reject) => {
      // 第一步：显示授权说明和链接
      const choice = dialog.showMessageBoxSync(null, {
        type: 'info',
        title: 'OneDrive Commander - Microsoft 授权',
        message: '获取OneDrive访问授权',
        detail: `请按以下步骤完成授权：\n\n` +
                `1️⃣ 点击"打开授权页面"按钮\n` +
                `2️⃣ 在浏览器中登录您的Microsoft账户\n` +
                `3️⃣ 确认授权OneDrive Commander访问权限\n` +
                `4️⃣ 复制重定向后URL中的授权码\n` +
                `5️⃣ 返回应用粘贴授权码\n\n` +
                `✅ 此次授权将有效90天\n` +
                `🔄 支持自动后台token刷新`,
        buttons: ['打开授权页面', '取消'],
        defaultId: 0,
        cancelId: 1,
        noLink: true
      })
      
      if (choice === 1) {
        reject(new Error('用户取消了授权'))
        return
      }
      
      // 打开授权页面
      shell.openExternal(authUrl)
      
      // 第二步：获取授权码
      this.promptForAuthCode(code_verifier, scopes, resolve, reject)
    })
  }
  
  /**
   * 提示用户输入授权码 - 1Panel风格 (UI和逻辑已恢复并修复)
   */
  private promptForAuthCode(
    code_verifier: string, 
    scopes: string[],
    resolve: (value: AuthenticationResult | PromiseLike<AuthenticationResult>) => void,
    reject: (reason?: any) => void
  ) {
    const { BrowserWindow, ipcMain } = require('electron')
    
    // 创建授权码输入窗口
    const inputWindow = new BrowserWindow({
      width: 600,
      height: 400,
      show: true,
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      autoHideMenuBar: true,
      webPreferences: {
        nodeIntegration: true, // 启用 node 集成以支持 IPC
        contextIsolation: false, // 禁用上下文隔离以简化通信
      },
      title: '输入Microsoft授权码',
      center: true
    })
    
    // 恢复了您想要的UI样式
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>授权码输入</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; background: #f8f9fa; margin: 0; padding: 20px; display: flex; flex-direction: column; height: 100vh; box-sizing: border-box; }
          .header { text-align: center; margin-bottom: 20px; }
          .title { font-size: 20px; font-weight: 600; color: #333; margin-bottom: 10px; }
          .subtitle { font-size: 14px; color: #666; line-height: 1.5; }
          .form-group { margin-bottom: 20px; }
          .label { display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px; }
          .auth-code-input { width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; font-family: 'Cascadia Code', 'SF Mono', monospace; background: white; box-sizing: border-box; resize: none; }
          .auth-code-input:focus { outline: none; border-color: #007aff; box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1); }
          .help-text { font-size: 12px; color: #888; margin-top: 8px; line-height: 1.4; }
          .button-group { display: flex; gap: 12px; margin-top: auto; justify-content: flex-end; }
          .button { padding: 10px 20px; border: none; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease; }
          .button-primary { background: #007aff; color: white; }
          .button-primary:hover { background: #0056b3; }
          .button-secondary { background: #f8f9fa; color: #666; border: 1px solid #dee2e6; }
          .button-secondary:hover { background: #e9ecef; }
          .error { color: #dc3545; font-size: 12px; margin-top: 8px; display: none; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title">🔐 Microsoft 授权码</div>
          <div class="subtitle">请从浏览器重定向后的URL中复制授权码<br>授权码通常是一串很长的字符串，以 "code=" 开头</div>
        </div>
        <div class="form-group">
          <label class="label" for="authCode">授权码 (Authorization Code)</label>
          <textarea id="authCode" class="auth-code-input" rows="4" placeholder="粘贴完整的授权码或包含code=的完整URL..."></textarea>
          <div class="help-text">💡 提示：可以粘贴完整的重定向URL，程序会自动提取授权码</div>
          <div id="error" class="error"></div>
        </div>
        <div class="button-group">
          <button class="button button-secondary" onclick="cancel()">取消</button>
          <button class="button button-primary" onclick="confirm()">确认授权</button>
        </div>
        <script>
          const { ipcRenderer } = require('electron');

          function extractAuthCode(input) {
            const codeMatch = input.match(/[?&]code=([^&]+)/);
            return codeMatch ? decodeURIComponent(codeMatch[1]) : input.trim();
          }

          function confirm() {
            const input = document.getElementById('authCode').value.trim();
            const errorDiv = document.getElementById('error');

            if (!input) {
              errorDiv.textContent = '请输入授权码';
              errorDiv.style.display = 'block';
              return;
            }

            const authCode = extractAuthCode(input);
            if (authCode.length < 10) {
              errorDiv.textContent = '授权码格式不正确，请检查输入';
              errorDiv.style.display = 'block';
              return;
            }

            console.log('Sending auth code via IPC:', authCode.substring(0, 20) + '...');
            ipcRenderer.send('auth-code-received', authCode);
          }

          function cancel() {
            console.log('User cancelled auth code input');
            ipcRenderer.send('auth-code-cancelled');
          }

          document.addEventListener('DOMContentLoaded', () => {
            console.log('Auth code window loaded');
            document.getElementById('authCode').focus();
            document.getElementById('authCode').addEventListener('keydown', (e) => {
              if (e.ctrlKey && e.key === 'Enter') confirm();
            });
          });
        </script>
      </body>
      </html>
    `;
    
    inputWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

    let isHandled = false; // Flag to fix the race condition

    const cleanup = () => {
      // This function MUST be defined before use
      ipcMain.removeListener('auth-code-received', authCodeHandler);
      ipcMain.removeListener('auth-code-cancelled', cancelHandler);
      if (inputWindow && !inputWindow.isDestroyed()) {
        inputWindow.close();
      }
    };

    const authCodeHandler = async (_event: any, authCode: string) => {
      if (isHandled) {
        console.log('⚠️ Auth code handler already processed, ignoring duplicate call');
        return;
      }
      isHandled = true;

      try {
        console.log('🔑 Authorization code received, length:', authCode.length);
        console.log('🔄 Exchanging authorization code for tokens...');
        const result = await this.exchangeCodeForTokens(authCode, code_verifier, scopes);
        console.log('✅ Token exchange successful');
        resolve(result);
      } catch (error) {
        console.error('❌ Token exchange failed:', error);
        reject(error);
      } finally {
        cleanup();
      }
    };

    const cancelHandler = () => {
      if (isHandled) return;
      isHandled = true;
      reject(new Error('用户取消了授权'));
      cleanup();
    };

    ipcMain.on('auth-code-received', authCodeHandler);
    ipcMain.on('auth-code-cancelled', cancelHandler);

    // 添加窗口加载完成的日志
    inputWindow.webContents.on('did-finish-load', () => {
      console.log('✅ Auth code input window loaded successfully');
    });

    inputWindow.on('closed', () => {
      if (!isHandled) {
        reject(new Error('授权窗口被关闭'));
      }
      // Ensure cleanup is always called
      cleanup();
    });
  }

  /**
   * 优化的Device Code Flow - 强化refresh token持久性和用户体验 (备用方案)
   */
  private async authenticateWithOptimizedDeviceCode(scopes: string[]): Promise<AuthenticationResult> {
    const { shell, dialog } = require('electron')
    
    return new Promise((resolve, reject) => {
      const deviceCodeRequest = {
        scopes: scopes,
        deviceCodeCallback: (response: any) => {
          console.log('Device code received:', response.userCode)
          
          // 现代化对话框，强调长期有效性
          const choice = dialog.showMessageBoxSync(null, {
            type: 'info',
            title: 'Microsoft 登录 - 长期授权',
            message: '获取90天有效期授权',
            detail: `请在浏览器中完成登录：\n\n` +
                    `🔗 访问：${response.verificationUri}\n` +
                    `🔑 输入验证码：${response.userCode}\n` +
                    `👤 使用Microsoft账户登录\n\n` +
                    `✅ 此次登录将持续90天有效\n` +
                    `🔄 支持自动后台续期\n` +
                    `🚀 无需频繁重新登录`,
            buttons: ['打开浏览器并复制验证码', '手动操作', '取消'],
            defaultId: 0,
            cancelId: 2,
            noLink: true
          })

          if (choice === 0) {
            // 同时打开浏览器并复制验证码
            const { clipboard } = require('electron')
            clipboard.writeText(response.userCode)
            shell.openExternal(response.verificationUri)
            
            // 显示成功复制提示
            dialog.showMessageBoxSync(null, {
              type: 'info',
              title: '验证码已复制',
              message: `验证码 "${response.userCode}" 已复制到剪贴板`,
              detail: `浏览器已打开登录页面，直接粘贴验证码即可。\n\n登录成功后，此窗口将自动关闭。`,
              buttons: ['确定']
            })
          } else if (choice === 1) {
            shell.openExternal(response.verificationUri)
          } else {
            reject(new Error('用户取消了登录'))
            return
          }
        },
      }

      this.msalApp.acquireTokenByDeviceCode(deviceCodeRequest)
        .then((result) => {
          if (!result) {
            reject(new Error('Device code authentication returned null result'))
            return
          }

          console.log('🎉 Device Code authentication successful!')
          console.log('📅 Access token expires:', result.expiresOn)
          console.log('🔐 Account ID:', result.account?.homeAccountId)

          // 验证refresh token获取情况
          if (result.account && result.account.homeAccountId) {
            console.log('✅ Account cached - long-term refresh token available')
            console.log('🔄 Auto-refresh will maintain 90-day session')
          }

          resolve(result)
        })
        .catch((error) => {
          console.error('❌ Device code authentication failed:', error)
          reject(error)
        })
    })
  }

  /**
   * Authorization Code Flow with PKCE + 本地服务器处理回调 (备用方案)
   */
  private async authenticateWithAuthCodeFlow(scopes: string[]): Promise<AuthenticationResult> {
    const { BrowserWindow } = require('electron')
    const crypto = require('crypto')
    const http = require('http')
    const url = require('url')
    
    return new Promise((resolve, reject) => {
      // 生成PKCE参数
      const code_verifier = crypto.randomBytes(32).toString('base64url')
      const code_challenge = crypto.createHash('sha256').update(code_verifier).digest('base64url')
      
      // 定义redirectUri在外部作用域
      const redirectUri = `http://localhost`
      
      // 创建本地HTTP服务器处理回调
      const server = http.createServer((req: any, res: any) => {
        const parsedUrl = url.parse(req.url, true)
        
        // Microsoft Graph Explorer重定向到根路径并带有查询参数
        if (parsedUrl.pathname === '/' && (parsedUrl.query.code || parsedUrl.query.error)) {
          const code = parsedUrl.query.code
          const error = parsedUrl.query.error
          
          // 返回成功页面
          res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' })
          res.end(`
            <!DOCTYPE html>
            <html>
            <head>
              <title>登录成功</title>
              <style>
                body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
                       text-align: center; padding: 50px; background: #f5f5f5; }
                .success { background: #d4edda; color: #155724; padding: 20px; 
                          border-radius: 8px; display: inline-block; }
              </style>
            </head>
            <body>
              <div class="success">
                <h2>✅ 登录成功！</h2>
                <p>您可以关闭此页面，返回 OneDrive Commander。</p>
                <p>此次授权将有效90天。</p>
              </div>
              <script>setTimeout(() => window.close(), 3000);</script>
            </body>
            </html>
          `)
          
          // 关闭服务器
          server.close()
          
          if (error) {
            reject(new Error(`OAuth error: ${error}`))
            return
          }
          
          if (code) {
            // 使用授权码换取token
            this.exchangeCodeForTokens(code, code_verifier, scopes, redirectUri)
              .then(resolve)
              .catch(reject)
          }
        }
      })
      
      // 使用端口3000，避免权限问题
      const port = 3000
      server.listen(port, 'localhost', () => {
        
        console.log(`🚀 Local callback server started on port ${port}`)
        
        // 构建授权URL
        const authUrl = `${this.config.authority}/oauth2/v2.0/authorize?` +
          `client_id=${this.config.clientId}&` +
          `response_type=code&` +
          `redirect_uri=${encodeURIComponent(redirectUri)}&` +
          `scope=${encodeURIComponent(scopes.join(' '))}&` +
          `code_challenge=${code_challenge}&` +
          `code_challenge_method=S256&` +
          `response_mode=query&` +
          `prompt=select_account`

        console.log('🔐 Opening Microsoft login in browser...')

        // 创建授权窗口
        const authWindow = new BrowserWindow({
          width: 500,
          height: 700,
          show: true,
          resizable: true,
          minimizable: true,
          maximizable: true,
          alwaysOnTop: false,
          autoHideMenuBar: true,
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
          },
          title: 'Microsoft 登录 - OneDrive Commander',
          center: true
        })

        authWindow.loadURL(authUrl)

        // 窗口关闭处理
        authWindow.on('closed', () => {
          server.close()
          reject(new Error('用户取消了登录'))
        })
        
        // 监听服务器错误
        server.on('error', (err: any) => {
          reject(new Error(`服务器错误: ${err.message}`))
        })
      })
    })
  }

  /**
   * 使用授权码换取长期token (包含1Panel风格的重载)
   */
  private async exchangeCodeForTokens(
    code: string,
    code_verifier: string,
    scopes: string[],
    redirectUri?: string
  ): Promise<AuthenticationResult> {
    try {
      console.log('🔄 Token exchange method:', redirectUri ? 'Standard MSAL' : 'Manual HTTP');
      console.log('🔧 Using redirect URI:', redirectUri || this.config.redirectUri);

      // 优先使用 MSAL 标准方式，无论是否有 redirectUri
      const tokenRequest = {
        code: code,
        scopes: scopes,
        redirectUri: redirectUri || this.config.redirectUri,
        codeVerifier: code_verifier,
      }

      console.log('📤 Sending token request to MSAL...');
      const response = await this.msalApp.acquireTokenByCode(tokenRequest)
      console.log('🎉 MSAL Authorization Code Flow successful!')
      console.log('📅 Access token expires:', response.expiresOn)
      console.log('🔐 Account ID:', response.account?.homeAccountId)
      console.log('✅ 90-day refresh token obtained and securely cached!')
      console.log('🔄 Auto-refresh will maintain long-term session')
      return response
    } catch (error) {
      console.error('❌ Token exchange failed:', error)
      throw error
    }
  }
  
  /**
   * 使用客户端密码直接请求token
   */
  private async requestTokenWithClientSecret(tokenData: any): Promise<any> {
    const https = require('https')
    const querystring = require('querystring')
    
    const postData = querystring.stringify(tokenData)
    
    const options = {
      hostname: 'login.microsoftonline.com',
      port: 443,
      path: '/common/oauth2/v2.0/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData)
      }
    }
    
    return new Promise((resolve, reject) => {
      const req = https.request(options, (res: any) => {
        let data = ''
        
        res.on('data', (chunk: any) => {
          data += chunk
        })
        
        res.on('end', () => {
          try {
            const response = JSON.parse(data)
            
            if (response.error) {
              reject(new Error(`Token request failed: ${response.error_description || response.error}`))
            } else {
              resolve(response)
            }
          } catch (parseError) {
            reject(new Error(`Failed to parse token response: ${parseError}`))
          }
        })
      })
      
      req.on('error', (error: any) => {
        reject(new Error(`Token request failed: ${error.message}`))
      })
      
      req.write(postData)
      req.end()
    })
  }

  /**
   * 更新token缓存
   */
  private updateTokenCache(result: AuthenticationResult): void {
    this.currentAccessToken = result.accessToken
    this.tokenExpiresAt = result.expiresOn ? result.expiresOn.getTime() : null
    
    // 尝试从账户信息中获取refresh token
    if (result.account && result.account.homeAccountId) {
      // MSAL会自动管理refresh token，我们主要管理access token
      console.log('Token updated successfully')
      console.log('Access token expires at:', result.expiresOn ? new Date(result.expiresOn).toLocaleString() : 'Unknown')
      
      if (this.tokenExpiresAt) {
        const remainingMinutes = Math.floor((this.tokenExpiresAt - Date.now()) / (1000 * 60))
        console.log('Token valid for:', remainingMinutes, 'minutes')
      }
    }
  }

  /**
   * 优化的自动刷新机制 - 确保长期有效性
   */
  private setupAutoRefresh(): void {
    // 清除现有的定时器
    if (this.autoRefreshTimer) {
      clearTimeout(this.autoRefreshTimer)
    }

    if (!this.tokenExpiresAt) {
      console.log('No token expiry time, skipping auto-refresh setup')
      return
    }

    // 在token过期前10分钟开始尝试刷新，但至少30秒后开始
    const refreshTime = Math.max(
      this.tokenExpiresAt - Date.now() - (10 * 60 * 1000), // 10分钟前
      30 * 1000 // 最少30秒后
    )
    
    if (refreshTime > 0) {
      const refreshMinutes = Math.floor(refreshTime / (1000 * 60))
      console.log(`🔄 Auto-refresh scheduled in ${refreshMinutes} minutes`)
      console.log(`📅 Token expires at: ${new Date(this.tokenExpiresAt).toLocaleString()}`)
      
      this.autoRefreshTimer = setTimeout(async () => {
        try {
          console.log('🔄 Starting automatic token refresh...')
          await this.refreshTokenSilently()
          console.log('✅ Auto-refresh successful - token extended')
        } catch (error) {
          console.error('❌ Automatic token refresh failed:', error)
          console.log('Will attempt refresh on next app start or manual refresh')
          
          // 设置一个更频繁的重试机制
          setTimeout(() => {
            this.setupAutoRefresh()
          }, 5 * 60 * 1000) // 5分钟后重试
        }
      }, refreshTime)
    } else {
      console.log('⚠️ Token already expired or expiring soon, attempting immediate refresh')
      this.refreshTokenSilently().catch(error => {
        console.error('Immediate refresh failed:', error)
      })
    }
  }

  /**
   * 静默刷新token
   */
  private async refreshTokenSilently(): Promise<void> {
    try {
      const accounts = await this.msalApp.getTokenCache().getAllAccounts()
      if (accounts.length === 0) {
        throw new Error('No accounts found for refresh')
      }

      const refreshRequest = {
        account: accounts[0],
        scopes: [
          'https://graph.microsoft.com/Files.ReadWrite.All',
          'https://graph.microsoft.com/User.Read',
          'offline_access'
        ],
        forceRefresh: true
      }

      const response = await this.msalApp.acquireTokenSilent(refreshRequest)
      this.updateTokenCache(response)
      this.setupAutoRefresh() // 设置下一次自动刷新
      
      console.log('Token refreshed successfully in background')
    } catch (error) {
      console.error('Background token refresh failed:', error)
      throw error
    }
  }

  /**
   * 检查当前token是否过期
   */
  private isCurrentTokenExpired(): boolean {
    if (!this.tokenExpiresAt) {
      return true
    }
    // 提前5分钟判断为过期，留出刷新时间
    const expirationBuffer = 5 * 60 * 1000 // 5分钟
    return Date.now() >= (this.tokenExpiresAt - expirationBuffer)
  }

  /**
   * 获取当前有效的access token
   */
  async getAccessToken(): Promise<string | null> {
    // 检查当前token是否过期
    if (this.currentAccessToken && !this.isCurrentTokenExpired()) {
      return this.currentAccessToken
    }

    // Token过期或不存在，尝试静默刷新
    try {
      await this.refreshTokenSilently()
      return this.currentAccessToken
    } catch (error) {
      console.error('Failed to refresh access token:', error)
      this.clearAccessToken()
      return null
    }
  }

  /**
   * 设置访问令牌（用于外部设置）
   */
  setAccessToken(accessToken: string, expiresAt?: number): void {
    this.currentAccessToken = accessToken
    this.tokenExpiresAt = expiresAt || null
    console.log('Access token set manually, expires at:', expiresAt ? new Date(expiresAt).toLocaleString() : 'Unknown')
    
    // 设置自动刷新
    this.setupAutoRefresh()
  }

  /**
   * 清除当前访问令牌
   */
  clearAccessToken(): void {
    this.currentAccessToken = null
    this.tokenExpiresAt = null
    this.currentRefreshToken = null
    
    // 清除自动刷新定时器
    if (this.autoRefreshTimer) {
      clearTimeout(this.autoRefreshTimer)
      this.autoRefreshTimer = null
    }
    
    console.log('Access token cleared')
  }

  /**
   * 刷新令牌（外部调用）
   */
  async refreshToken(refreshToken?: string): Promise<{ accessToken: string; refreshToken?: string; expiresAt?: number }> {
    try {
      console.log('Attempting to refresh token...')
      await this.refreshTokenSilently()
      
      if (!this.currentAccessToken) {
        throw new Error('Failed to obtain new access token')
      }
      
      console.log('Token refresh successful')
      return {
        accessToken: this.currentAccessToken,
        refreshToken: refreshToken, // MSAL自动管理refresh token
        expiresAt: this.tokenExpiresAt || undefined
      }
    } catch (error) {
      console.error('Failed to refresh token:', error)
      this.clearAccessToken()
      throw new Error('令牌刷新失败，需要重新登录')
    }
  }

  /**
   * 获取token剩余有效时间（分钟）
   */
  getTokenRemainingTime(): number {
    if (!this.tokenExpiresAt) {
      return 0
    }
    const remaining = this.tokenExpiresAt - Date.now()
    return Math.max(0, Math.floor(remaining / (1000 * 60)))
  }

  /**
   * 检查是否有有效的refresh token
   */
  async hasValidRefreshToken(): Promise<boolean> {
    try {
      const accounts = await this.msalApp.getTokenCache().getAllAccounts()
      return accounts.length > 0
    } catch (error) {
      return false
    }
  }

  /**
   * 退出登录
   */
  async logout(): Promise<void> {
    // 清除所有token缓存
    this.clearAccessToken()
    
    // 清除MSAL缓存
    try {
      const accounts = await this.msalApp.getTokenCache().getAllAccounts()
      for (const account of accounts) {
        await this.msalApp.getTokenCache().removeAccount(account)
      }
    } catch (error) {
      console.error('Error clearing MSAL cache:', error)
    }
    
    console.log('User logged out, all tokens cleared')
  }
} 