import { app, shell, BrowserWindow, ipcMain, dialog } from 'electron'
import { join } from 'path'
import * as fs from 'fs'
import * as path from 'path'
import { AuthService } from './services/authService'
import { GraphService } from './services/graphService'
import { ConfigService } from './services/configService'
import { SessionService } from './services/sessionService'
import { APP_CONFIG } from './config'

// Initialize services
const configService = new ConfigService()
const sessionService = new SessionService()

// 使用 ConfigService 的配置来初始化 AuthService，而不是静态配置
let authService: AuthService
let graphService: GraphService

// 延迟初始化认证服务，确保使用正确的配置
function initializeAuthServices() {
  const config = configService.getConfig()

  // 如果没有配置认证信息，使用默认配置
  const authConfig = {
    clientId: config.auth?.clientId || APP_CONFIG.auth.clientId,
    clientSecret: config.auth?.clientSecret || APP_CONFIG.auth.clientSecret,
    authority: config.auth?.authority || APP_CONFIG.auth.authority,
    redirectUri: config.auth?.redirectUri || APP_CONFIG.auth.redirectUri
  }

  authService = new AuthService(authConfig)
  graphService = new GraphService(authService)

  console.log('🔧 Auth services initialized with config:', {
    clientId: authConfig.clientId.substring(0, 8) + '...',
    hasClientSecret: !!authConfig.clientSecret,
    authority: authConfig.authority,
    redirectUri: authConfig.redirectUri
  })
}

function createWindow(): void {
  const config = configService.getConfig()
  
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: config.windowBounds.width,
    height: config.windowBounds.height,
    x: config.windowBounds.x,
    y: config.windowBounds.y,
    minWidth: 800,
    minHeight: 600,
    show: false,
    autoHideMenuBar: true,
    icon: join(__dirname, '../../resources/icon.png'),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: false
    }
  })

  // 保存窗口位置和大小
  mainWindow.on('close', () => {
    const bounds = mainWindow.getBounds()
    configService.updateConfig({
      windowBounds: {
        width: bounds.width,
        height: bounds.height,
        x: bounds.x,
        y: bounds.y
      }
    })
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
    // 只有在开发者模式开启时才自动打开开发者工具
    const config = configService.getConfig()
    if (process.env.NODE_ENV === 'development' && config.developerMode) {
      mainWindow.webContents.openDevTools()
    }
  })



  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 添加快捷键支持
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // 检查是否是开发者工具快捷键
    const isDevToolsShortcut = (input.control && input.shift && input.key.toLowerCase() === 'i') || input.key === 'F12'
    
    if (isDevToolsShortcut) {
      // 只有在开发者模式开启时才允许打开开发者工具
      const config = configService.getConfig()
      console.log('Developer tools shortcut triggered, developer mode status:', config.developerMode)
      
      if (config.developerMode) {
        mainWindow.webContents.toggleDevTools()
      } else {
        console.log('Developer mode not enabled, ignoring developer tools shortcut')
        event.preventDefault()
      }
    }
  })

  // Load the remote URL for development or the local html file for production.
  if (process.env.NODE_ENV === 'development' && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
app.whenReady().then(() => {
  // Set app user model id for windows
  app.setAppUserModelId('com.od-commander')

  // 初始化认证服务
  initializeAuthServices()

  // IPC test
  ipcMain.handle('ping', () => 'pong')

  // Dialog handlers
  ipcMain.handle('dialog:showSaveDialog', async (event, options) => {
    const result = await dialog.showSaveDialog(options)
    return result
  })

  ipcMain.handle('dialog:showOpenDialog', async (event, options) => {
    const result = await dialog.showOpenDialog(options)
    return result
  })

  // Config handlers
  ipcMain.handle('config:get', () => {
    return configService.getConfig()
  })

  ipcMain.handle('config:update', (event, updates) => {
    try {
      configService.updateConfig(updates)
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  })

  ipcMain.handle('config:reset', () => {
    try {
      configService.resetConfig()
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  })

  ipcMain.handle('config:get-path', () => {
    return configService.getConfigPath()
  })

  ipcMain.handle('config:set-custom-path', (event, customPath) => {
    try {
      configService.setCustomConfigPath(customPath)
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('config:export', (event, exportPath) => {
    try {
      configService.exportConfig(exportPath)
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('config:import', (event, importPath) => {
    try {
      configService.importConfig(importPath)
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('config:clear-cache', () => {
    try {
      configService.clearCache()
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// IPC handlers for authentication and file operations
ipcMain.handle('auth:login', async (event, forceNewLogin: boolean = false) => {
  try {
    console.log('🔐 Starting login process, forceNewLogin:', forceNewLogin)

    // 检查认证服务是否正确初始化
    if (!authService) {
      throw new Error('认证服务未初始化，请重启应用')
    }

    const result = await authService.authenticate(forceNewLogin)
    console.log('✅ Authentication successful, getting user info...')

    const userInfo = await graphService.getUserInfo()
    const userProfile = await graphService.getUserProfile()

    console.log('✅ User info retrieved:', userProfile.displayName)
    console.log('✅ Login completed successfully - single account mode')

    // 检查是否启用了记住登录
    const config = configService.getConfig()
    console.log('📋 Login completed, checking rememberLogin setting:', config.rememberLogin)

    if (config.rememberLogin) {
      console.log('💾 Saving session for auto-login...')
      try {
        // 保存会话信息，包括refresh token状态
        sessionService.saveSession({
          isAuthenticated: true,
          userInfo: {
            id: userInfo.id,
            displayName: userInfo.displayName,
            userPrincipalName: userInfo.userPrincipalName,
            mail: userInfo.mail
          },
          refreshTokenInfo: {
            hasRefreshToken: !!result.account,
            accountId: result.account?.homeAccountId
          }
        })
        console.log('✅ Session saved successfully for auto-login')
        console.log('🔑 Refresh token info saved:', {
          hasRefreshToken: !!result.account,
          accountId: result.account?.homeAccountId?.substring(0, 10) + '...'
        })
      } catch (sessionError) {
        console.error('❌ Failed to save session:', sessionError)
      }
    } else {
      console.log('⚠️ Remember login is disabled, session will not be saved')
    }

    console.log('🎉 Login process completed successfully')
    return {
      success: true,
      accessToken: result.accessToken,
      userInfo: userInfo,
      profile: userProfile
    }
  } catch (error) {
    console.error('❌ Authentication failed:', error)

    // 提供更详细的错误信息
    let errorMessage = 'Authentication failed'
    if (error instanceof Error) {
      errorMessage = error.message

      // 针对常见错误提供更友好的提示
      if (error.message.includes('用户取消')) {
        errorMessage = '用户取消了登录操作'
      } else if (error.message.includes('AADSTS')) {
        errorMessage = 'Microsoft 认证服务错误，请检查网络连接或稍后重试'
      } else if (error.message.includes('clientId')) {
        errorMessage = 'Azure 应用配置错误，请检查客户端 ID 设置'
      } else if (error.message.includes('authority')) {
        errorMessage = '认证服务器配置错误，请检查设置'
      }
    }

    return {
      success: false,
      error: errorMessage
    }
  }
})

ipcMain.handle('auth:logout', async () => {
  try {
    await authService.logout()
    // 清除保存的会话
    sessionService.clearSession()
    console.log('✅ User logged out successfully and session cleared')
    return { success: true }
  } catch (error) {
    console.error('Logout failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Logout failed'
    }
  }
})

// 自动登录 - 改进版本，结合会话管理和 MSAL 静默认证
ipcMain.handle('auth:auto-login', async () => {
  try {
    console.log('🔄 Starting auto-login process...')

    // 首先检查配置是否启用了记住登录
    const config = configService.getConfig()
    console.log('📋 Config loaded, rememberLogin:', config.rememberLogin)

    if (!config.rememberLogin) {
      console.log('❌ Auto-login disabled in config')
      return { success: false, error: 'Auto-login disabled' }
    }

    // 检查保存的会话是否有效
    console.log('🔍 Checking session validity...')
    const sessionValid = sessionService.isSessionValid()
    console.log('📄 Session valid:', sessionValid)

    if (!sessionValid) {
      console.log('❌ No valid session found')
      return { success: false, error: 'No valid session available' }
    }

    console.log('✅ Valid session found, proceeding with MSAL authentication...')

    // 尝试使用 MSAL 的纯静默认证（不会弹出任何对话框）
    console.log('🔐 Attempting MSAL silent authentication...')
    const result = await authService.authenticateSilentOnly()

    if (result) {
      console.log('✅ MSAL silent authentication successful')
      console.log('📊 Getting user info and profile...')

      const userInfo = await graphService.getUserInfo()
      const userProfile = await graphService.getUserProfile()

      console.log('👤 User info retrieved:', {
        id: userInfo.id,
        displayName: userInfo.displayName,
        userPrincipalName: userInfo.userPrincipalName
      })

      // 更新会话信息
      sessionService.updateUserInfo({
        id: userInfo.id,
        displayName: userInfo.displayName,
        userPrincipalName: userInfo.userPrincipalName,
        mail: userInfo.mail
      })

      console.log('✅ Auto-login completed successfully')
      return {
        success: true,
        accessToken: result.accessToken,
        userInfo: userInfo,
        profile: userProfile,
        isAutoLogin: true
      }
    } else {
      console.log('❌ MSAL silent authentication failed: No cached tokens available')
      console.log('🧹 Clearing invalid session...')
      // 如果MSAL认证失败，清除会话
      sessionService.clearSession()
      return { success: false, error: 'No cached authentication available' }
    }
  } catch (error) {
    console.log('❌ Auto-login failed:', error instanceof Error ? error.message : String(error))
    // 认证失败时清除会话
    sessionService.clearSession()
    return {
      success: false,
      error: 'Auto login failed - please log in manually'
    }
  }
})

// 检查会话状态
ipcMain.handle('auth:check-session', async () => {
  try {
    console.log('🔍 Checking session status...')
    const config = configService.getConfig()
    console.log('📋 Config rememberLogin:', config.rememberLogin)

    if (!config.rememberLogin) {
      console.log('❌ Auto-login disabled in config')
      return { success: false, error: 'Auto-login disabled' }
    }

    const session = sessionService.getSession()
    const isValid = sessionService.isSessionValid()

    console.log('📄 Session check result:', {
      hasSession: !!session,
      isAuthenticated: session.isAuthenticated,
      isValid: isValid,
      userDisplayName: session.userInfo?.displayName
    })

    return {
      success: true,
      hasValidSession: isValid,
      sessionData: isValid ? session : null
    }
  } catch (error) {
    console.error('❌ Failed to check session:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check session'
    }
  }
})

// 获取会话文件路径（用于调试）
ipcMain.handle('auth:get-session-path', async () => {
  try {
    const userDataPath = app.getPath('userData')
    const sessionPath = path.join(userDataPath, 'session', 'session.json')
    return {
      success: true,
      sessionPath,
      userDataPath,
      exists: fs.existsSync(sessionPath)
    }
  } catch (error) {
    console.error('Failed to get session path:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get session path'
    }
  }
})

// 获取MSAL缓存状态（用于调试）
ipcMain.handle('auth:get-msal-cache-status', async () => {
  try {
    const cacheStatus = await authService.getCacheStatus()
    return {
      success: true,
      cacheStatus
    }
  } catch (error) {
    console.error('Failed to get MSAL cache status:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get MSAL cache status'
    }
  }
})

ipcMain.handle('files:list', async (event, path: string = '') => {
  try {
    const items = await graphService.getDriveItems(path)
    return {
      success: true,
      items: items
    }
  } catch (error) {
    console.error('Failed to list files:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to list files'
    }
  }
})

ipcMain.handle('files:create-folder', async (event, name: string, parentPath: string = '') => {
  try {
    const folder = await graphService.createFolder(name, parentPath)
    return {
      success: true,
      folder: folder
    }
  } catch (error) {
    console.error('Failed to create folder:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create folder'
    }
  }
})

ipcMain.handle('files:upload', async (event, filePath: string, fileName: string, parentPath: string = '') => {
  try {
    const file = await graphService.uploadFile(filePath, fileName, parentPath)
    return {
      success: true,
      file: file
    }
  } catch (error) {
    console.error('Failed to upload file:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload file'
    }
  }
})

ipcMain.handle('files:upload-buffer', async (event, fileData: number[], fileName: string, parentPath: string = '') => {
  try {
    const buffer = Buffer.from(fileData)
    const file = await graphService.uploadFileBuffer(buffer, fileName, parentPath)
    return {
      success: true,
      file: file
    }
  } catch (error) {
    console.error('Failed to upload file buffer:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload file buffer'
    }
  }
})

ipcMain.handle('files:download', async (event, itemId: string, localPath: string) => {
  try {
    await graphService.downloadFile(itemId, localPath)
    return { success: true }
  } catch (error) {
    console.error('Failed to download file:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to download file'
    }
  }
})

ipcMain.handle('files:delete', async (event, itemId: string) => {
  try {
    await graphService.deleteItem(itemId)
    return { success: true }
  } catch (error) {
    console.error('Failed to delete item:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete item'
    }
  }
})

ipcMain.handle('files:search', async (event, query: string) => {
  try {
    const items = await graphService.searchFiles(query)
    return {
      success: true,
      items: items
    }
  } catch (error) {
    console.error('Failed to search files:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to search files'
    }
  }
})

ipcMain.handle('user:get-storage-info', async () => {
  try {
    const storageInfo = await graphService.getStorageInfo()
    return {
      success: true,
      storageInfo: storageInfo
    }
  } catch (error) {
    console.error('Failed to get storage info:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get storage info'
    }
  }
})

ipcMain.handle('user:get-profile', async () => {
  try {
    const profile = await graphService.getUserProfile()
    return {
      success: true,
      profile: profile
    }
  } catch (error) {
    console.error('Failed to get user profile:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user profile'
    }
  }
})

ipcMain.handle('user:get-photo', async (event, size = '120x120') => {
  try {
    const photo = await graphService.getUserPhoto(size)
    return {
      success: true,
      photo: photo
    }
  } catch (error) {
    console.error('Failed to get user photo:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user photo'
    }
  }
})



// 多账号管理功能已删除 - 现在使用单账号模式


// In this file you can include the rest of your app's main process code. 